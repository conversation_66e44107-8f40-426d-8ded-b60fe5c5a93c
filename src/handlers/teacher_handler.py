"""
老师标签搜索处理器

处理老师标签搜索相关的命令和交互
"""

from typing import List
from telegram import Update
from telegram.ext import ContextTypes, filters
from src.core.decorators import message_handler, command_handler
from src.core.teacher_manager import teacher_manager
from src.core.logger import default_logger as logger


async def delete_message_by_id(context: ContextTypes.DEFAULT_TYPE, chat_id: int, message_id: int):
    """删除指定消息的辅助函数"""
    try:
        await context.bot.delete_message(chat_id=chat_id, message_id=message_id)
        logger.debug(f"已删除搜索结果消息: chat_id={chat_id}, message_id={message_id}")
    except Exception as e:
        logger.warning(f"删除搜索结果消息失败: {e}")


def escape_markdown_v2(text: str) -> str:
    """
    转义 Markdown V2 特殊字符

    Args:
        text: 需要转义的文本

    Returns:
        转义后的文本
    """
    if not text:
        return text

    # Telegram Markdown V2 需要转义的字符
    escape_chars = r'_*[]()~`>#+-=|{}.!'

    # 转义特殊字符
    for char in escape_chars:
        text = text.replace(char, f'\\{char}')

    return text


def parse_command_args(args: List[str]) -> List[str]:
    """
    解析命令参数为标签列表

    Args:
        args: 命令参数列表

    Returns:
        标签列表
    """
    if not args:
        return []

    # 将所有参数合并为一个字符串，然后按逗号分割（兼容中文和英文逗号）
    tags_text = ' '.join(args)
    # 先替换中文逗号为英文逗号，然后分割
    tags_text = tags_text.replace('，', ',')
    tags = [tag.strip() for tag in tags_text.split(',') if tag.strip()]

    return tags


def format_teacher_list_item(teacher, index: int) -> str:
    """
    格式化单个老师信息项

    Args:
        teacher: 老师对象
        index: 序号

    Returns:
        格式化的老师信息字符串
    """
    teacher_info = f"{index}\\. "

    # 添加昵称（转义特殊字符）
    escaped_nickname = escape_markdown_v2(teacher.nickname)
    teacher_info += f"*{escaped_nickname}*"

    # 添加用户名
    if teacher.username:
        escaped_username = escape_markdown_v2(teacher.username)
        teacher_info += f" \\(@{escaped_username}\\)"

    # 添加频道链接
    if teacher.channel_link:
        escaped_link = escape_markdown_v2(teacher.channel_link)
        teacher_info += f"\n   📺 {escaped_link}"

    # 添加标签
    if teacher.tags:
        escaped_tags = [escape_markdown_v2(tag) for tag in teacher.tags]
        teacher_info += f"\n   🏷️ {', '.join(escaped_tags)}"

    return teacher_info


def format_teacher_result(teachers: List) -> str:
    """
    格式化老师搜索结果

    Args:
        teachers: 老师列表

    Returns:
        格式化的结果字符串
    """
    if not teachers:
        return "暂无匹配结果"

    result_lines = [f"🔍 找到 {len(teachers)} 位老师：\n"]

    for i, teacher in enumerate(teachers, 1):
        teacher_info = format_teacher_list_item(teacher, i)
        # 为搜索结果添加"频道:"和"标签:"前缀
        teacher_info = teacher_info.replace("\n   📺 ", "\n   📺 频道: ")
        teacher_info = teacher_info.replace("\n   🏷️ ", "\n   🏷️ 标签: ")
        result_lines.append(teacher_info)

    return "\n\n".join(result_lines)


# 群组命令：搜索老师标签
@command_handler("search_tag", "搜索老师标签", priority=10)
async def handle_search_tag_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理 /search_tag 命令"""
    message = update.message
    if not message:
        return

    # 检查是否在群组中
    if message.chat.type not in ['group', 'supergroup']:
        group_only_message = await message.reply_text("❌ 此命令只能在群组中使用")

        # 群组限制消息也在1分钟后自动删除
        context.job_queue.run_once(
            callback=lambda context: delete_message_by_id(
                context, message.chat.id, group_only_message.message_id
            ),
            when=60  # 60秒 = 1分钟
        )

        # 1分钟后自动删除用户的搜索命令消息
        context.job_queue.run_once(
            callback=lambda context: delete_message_by_id(
                context, message.chat.id, message.message_id
            ),
            when=60  # 60秒 = 1分钟
        )
        return

    # 解析命令参数
    search_tags = parse_command_args(context.args)
    if not search_tags:
        usage_message = await message.reply_text(
            "❌ 请提供搜索标签\n\n"
            "用法：`/search\\_tag 标签1,标签2`\n"
            "示例：`/search\\_tag 数学,高中`",
            parse_mode='MarkdownV2'
        )

        # 使用说明消息也在1分钟后自动删除
        context.job_queue.run_once(
            callback=lambda context: delete_message_by_id(
                context, message.chat.id, usage_message.message_id
            ),
            when=60  # 60秒 = 1分钟
        )

        # 1分钟后自动删除用户的搜索命令消息
        context.job_queue.run_once(
            callback=lambda context: delete_message_by_id(
                context, message.chat.id, message.message_id
            ),
            when=60  # 60秒 = 1分钟
        )
        return

    logger.info(f"用户 {message.from_user.id} 在群组 {message.chat.id} 使用命令搜索标签: {search_tags}")

    try:
        # 搜索匹配的老师（匹配任意标签）
        matched_teachers = teacher_manager.search_by_tags(search_tags, match_all=False)

        # 格式化结果
        result_text = format_teacher_result(matched_teachers)

        # 回复搜索结果
        reply_message = await message.reply_text(
            result_text,
            parse_mode='MarkdownV2',
            disable_web_page_preview=True
        )

        # 1分钟后自动删除搜索结果消息
        context.job_queue.run_once(
            callback=lambda context: delete_message_by_id(
                context, message.chat.id, reply_message.message_id
            ),
            when=60  # 60秒 = 1分钟
        )

        # 1分钟后自动删除用户的搜索命令消息
        context.job_queue.run_once(
            callback=lambda context: delete_message_by_id(
                context, message.chat.id, message.message_id
            ),
            when=60  # 60秒 = 1分钟
        )

        logger.info(f"命令标签搜索完成，返回 {len(matched_teachers)} 个结果，搜索命令和结果消息将在1分钟后自动删除")

    except Exception as e:
        logger.error(f"处理命令标签搜索时出错: {e}")
        error_message = await message.reply_text("❌ 搜索时发生错误，请稍后重试")

        # 错误消息也在1分钟后自动删除
        context.job_queue.run_once(
            callback=lambda context: delete_message_by_id(
                context, message.chat.id, error_message.message_id
            ),
            when=60  # 60秒 = 1分钟
        )

        # 1分钟后自动删除用户的搜索命令消息
        context.job_queue.run_once(
            callback=lambda context: delete_message_by_id(
                context, message.chat.id, message.message_id
            ),
            when=60  # 60秒 = 1分钟
        )


# 管理员命令：添加老师
@command_handler("add_teacher", "添加老师", priority=10)
async def handle_add_teacher_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理添加老师命令（仅私聊）"""
    message = update.message
    if not message:
        return

    # 检查是否在私聊中
    if message.chat.type != 'private':
        await message.reply_text("❌ 此命令只能在私聊中使用")
        return

    # 解析命令参数
    # 格式: /add_teacher 昵称 @用户名 频道链接 标签1,标签2,标签3
    args = context.args

    if len(args) < 1:
        await message.reply_text(
            "❌ 命令格式错误\n\n"
            "正确格式：\n"
            "`/add\\_teacher 昵称 \\[@用户名\\] \\[频道链接\\] \\[标签1,标签2,标签3\\]`\n\n"
            "示例：\n"
            "`/add\\_teacher 张老师 @zhanglaoshi https://t\\.me/zhanglaoshi\\_channel 数学,高中,在线教学`",
            parse_mode='MarkdownV2'
        )
        return
    
    try:
        # 解析参数
        nickname = args[0]
        username = None
        channel_link = None
        tags = []

        # 解析可选参数
        for i in range(1, len(args)):
            arg = args[i].strip()
            if arg.startswith('@'):
                username = arg[1:]  # 去掉@符号
            elif arg.startswith('http'):
                channel_link = arg
            else:
                # 假设是标签（兼容中文和英文逗号）
                arg = arg.replace('，', ',')  # 替换中文逗号为英文逗号
                tags.extend([tag.strip() for tag in arg.split(',') if tag.strip()])
        
        # 创建老师对象
        from src.models.teacher import Teacher
        teacher = Teacher(
            nickname=nickname,
            username=username,
            channel_link=channel_link,
            tags=tags
        )
        
        # 添加到管理器
        teacher_id = teacher_manager.add_teacher(teacher)
        
        # 回复成功信息
        escaped_info = escape_markdown_v2(teacher.get_full_info())
        escaped_teacher_id = escape_markdown_v2(teacher_id)
        await message.reply_text(
            f"✅ 成功添加老师：\n\n"
            f"{escaped_info}\n\n"
            f"老师ID: `{escaped_teacher_id}`",
            parse_mode='MarkdownV2'
        )
        
        logger.info(f"用户 {message.from_user.id} 添加了老师: {teacher.nickname}")
        
    except Exception as e:
        logger.error(f"添加老师时出错: {e}")
        await message.reply_text(f"❌ 添加老师时发生错误: {str(e)}")


# 管理员命令：查看所有老师
@command_handler("list_teachers", "查看所有老师", priority=10)
async def handle_list_teachers_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理查看所有老师命令（群组和私聊均可使用）"""
    message = update.message
    if not message:
        return

    try:
        teachers = teacher_manager.get_all_teachers()

        if not teachers:
            empty_message = await message.reply_text("📝 老师标签库为空")

            # 2分钟后自动删除空库消息
            context.job_queue.run_once(
                callback=lambda context: delete_message_by_id(
                    context, message.chat.id, empty_message.message_id
                ),
                when=120  # 120秒 = 2分钟
            )

            # 2分钟后自动删除用户的命令消息
            context.job_queue.run_once(
                callback=lambda context: delete_message_by_id(
                    context, message.chat.id, message.message_id
                ),
                when=120  # 120秒 = 2分钟
            )
            return

        # 构建老师列表
        result_lines = [f"📚 老师标签库 \\(共 {len(teachers)} 位老师\\)：\n"]

        for i, teacher in enumerate(teachers, 1):
            teacher_info = format_teacher_list_item(teacher, i)
            result_lines.append(teacher_info)

        result_text = "\n\n".join(result_lines)

        # 如果消息太长，分批发送
        if len(result_text) > 4000:
            # 分批发送
            batch_size = 10
            reply_messages = []  # 存储所有回复消息，用于后续删除

            for i in range(0, len(teachers), batch_size):
                batch_teachers = teachers[i:i + batch_size]
                batch_lines = [f"📚 老师标签库 \\(第 {i//batch_size + 1} 批\\)：\n"]

                for j, teacher in enumerate(batch_teachers, i + 1):
                    teacher_info = format_teacher_list_item(teacher, j)
                    batch_lines.append(teacher_info)

                batch_text = "\n\n".join(batch_lines)
                reply_msg = await message.reply_text(batch_text, parse_mode='MarkdownV2', disable_web_page_preview=True)
                reply_messages.append(reply_msg)

            # 2分钟后自动删除所有批次消息
            for reply_msg in reply_messages:
                context.job_queue.run_once(
                    callback=lambda context, msg_id=reply_msg.message_id: delete_message_by_id(
                        context, message.chat.id, msg_id
                    ),
                    when=120  # 120秒 = 2分钟
                )
        else:
            reply_message = await message.reply_text(result_text, parse_mode='MarkdownV2', disable_web_page_preview=True)

            # 2分钟后自动删除回复消息
            context.job_queue.run_once(
                callback=lambda context: delete_message_by_id(
                    context, message.chat.id, reply_message.message_id
                ),
                when=120  # 120秒 = 2分钟
            )

        # 2分钟后自动删除用户的命令消息
        context.job_queue.run_once(
            callback=lambda context: delete_message_by_id(
                context, message.chat.id, message.message_id
            ),
            when=120  # 120秒 = 2分钟
        )

        chat_type = "群组" if message.chat.type in ['group', 'supergroup'] else "私聊"
        logger.info(f"用户 {message.from_user.id} 在{chat_type} {message.chat.id} 查看了老师列表，命令和结果消息将在2分钟后自动删除")

    except Exception as e:
        logger.error(f"查看老师列表时出错: {e}")
        error_message = await message.reply_text(f"❌ 查看老师列表时发生错误: {str(e)}")

        # 2分钟后自动删除错误消息
        context.job_queue.run_once(
            callback=lambda context: delete_message_by_id(
                context, message.chat.id, error_message.message_id
            ),
            when=120  # 120秒 = 2分钟
        )

        # 2分钟后自动删除用户的命令消息
        context.job_queue.run_once(
            callback=lambda context: delete_message_by_id(
                context, message.chat.id, message.message_id
            ),
            when=120  # 120秒 = 2分钟
        )


# 管理员命令：查看所有标签
@command_handler("list_tags", "查看所有标签", priority=10)
async def handle_list_tags_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """处理查看所有标签命令（仅私聊）"""
    message = update.message
    if not message:
        return

    # 检查是否在私聊中
    if message.chat.type != 'private':
        await message.reply_text("❌ 此命令只能在私聊中使用")
        return
    
    try:
        all_tags = teacher_manager.get_all_tags()
        teachers_count = teacher_manager.get_teachers_count()
        
        if not all_tags:
            await message.reply_text("🏷️ 暂无标签数据")
            return
        
        # 构建标签列表
        escaped_tags = [escape_markdown_v2(tag) for tag in all_tags]
        tags_text = ", ".join(escaped_tags)
        result_text = (
            f"🏷️ *所有标签* \\(共 {len(all_tags)} 个\\)：\n\n"
            f"{tags_text}\n\n"
            f"📊 统计信息：\n"
            f"• 老师总数：{teachers_count}\n"
            f"• 标签总数：{len(all_tags)}"
        )

        await message.reply_text(result_text, parse_mode='MarkdownV2')
        
        logger.info(f"用户 {message.from_user.id} 查看了标签列表")
        
    except Exception as e:
        logger.error(f"查看标签列表时出错: {e}")
        await message.reply_text(f"❌ 查看标签列表时发生错误: {str(e)}")
